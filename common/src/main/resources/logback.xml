<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">

    <!-- 通用日志格式 -->
    <property name="LOG_PATTERN" value="%highlight(%-5level) %cyan(%logger{10}(%F:%L\)) %msg%n"/>

    <!-- 应用名称变量 -->
    <property name="APP_NAME" value="myapp"/>

    <!-- 1. 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 4. 异步日志输出（提升性能） -->
    <appender name="ASYNC_CONSOLE" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>1024</queueSize> <!-- 队列容量 -->
        <includeCallerData>true</includeCallerData>
        <discardingThreshold>0</discardingThreshold> <!-- 队列剩余20%时丢弃TRACE/DEBUG日志 -->
        <appender-ref ref="CONSOLE"/>
    </appender>

    <!-- 根日志配置 -->
    <root level="INFO">
        <appender-ref ref="ASYNC_CONSOLE"/>
    </root>

    <!-- 包/类级别日志定制 -->
<!--    <logger name="com.yourcompany" level="DEBUG" additivity="false">-->
<!--        <appender-ref ref="ASYNC_CONSOLE"/>-->
<!--    </logger>-->
    <!-- 第三方库日志降噪 -->
</configuration>

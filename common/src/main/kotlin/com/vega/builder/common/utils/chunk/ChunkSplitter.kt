package com.vega.builder.common.utils.chunk

import com.google.gson.Gson
import com.vega.builder.common.utils.chunk.model.ChunkInfo
import com.vega.builder.common.utils.chunk.model.ChunkManifest
import com.vega.builder.common.utils.md5
import java.io.File
import java.io.FileOutputStream
import java.io.RandomAccessFile

object ChunkSplitter {
    /**
     * 带Manifest的文件分块
     * @param sourceFile 源文件路径
     * @param chunkDir 分块存储目录
     * @param baseKey 远端存储基础路径
     * @param chunkSize 分块大小(字节)，默认1MB
     * @return Manifest文件对象
     */
    fun splitFile(
        sourceFile: String,
        outputDirPath: String,
        baseKey: String,
        chunkSize: Int = 10 * 1024 * 1024 // 10MB 每个分片
    ): Pair<File, List<ChunkInfo>> {
        val source = File(sourceFile)
        require(source.exists()) { "file not found!" }
        require(source.length() > 0) { "file is empty!" }

        val outputDir = File(outputDirPath).apply { mkdirs() }
        require(outputDir.isDirectory) { "output file is not a directory!" }

        val originalName = source.name
        val totalSize = source.length()
        val chunkInfos = mutableListOf<ChunkInfo>()
        var chunkIndex = 0
        RandomAccessFile(source, "r").use { raf ->
            val buffer = ByteArray(chunkSize)

            while (true) {
                val bytesRead = raf.read(buffer)
                if (bytesRead == -1) break

                // 创建分块文件
                val chunkName = "${originalName}.part.$chunkIndex"
                val chunkFile = File(outputDir, chunkName)
                FileOutputStream(chunkFile).use { it.write(buffer, 0, bytesRead) }

                // 构建分块信息
                val remoteKey = "$baseKey/part/$chunkIndex"
                chunkInfos.add(
                    ChunkInfo(
                        index = chunkIndex,
                        name = chunkName,
                        size = bytesRead.toLong(),
                        md5 = chunkFile.md5(),
                        remoteKey = remoteKey,
                        file = chunkFile
                    )
                )

                chunkIndex++
            }
        }

        // 创建Manifest文件
        val manifestFile = File(outputDir, "$originalName.manifest.json")
        manifestFile.writeText(
            Gson().toJson(
                ChunkManifest(
                    originalName = originalName,
                    totalSize = totalSize,
                    chunkSize = chunkSize,
                    totalChunks = chunkIndex,
                    md5 = source.md5(),
                    chunks = chunkInfos
                )
            )
        )
        return manifestFile to chunkInfos
    }
}
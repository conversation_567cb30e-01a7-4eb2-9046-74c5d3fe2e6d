package com.vega.builder.common.utils.chunk

import com.google.gson.Gson
import com.vega.builder.common.logger.logger
import com.vega.builder.common.utils.chunk.model.ChunkManifest
import com.vega.builder.common.utils.fromJson
import com.vega.builder.common.utils.md5
import java.io.File
import kotlinx.serialization.json.Json

object ChunkMerger {
    /**
     * 通过Manifest文件合并分块
     * @param manifestFile Manifest文件路径
     * @param outputFile 合并后的输出文件路径
     */
    fun mergeWithManifest(
        manifest: ChunkManifest,
        outputFile: String,
    ) {
        val targetFile = File(outputFile).apply { parentFile?.mkdirs() }


        targetFile.outputStream().use { output ->
            manifest.chunks.sortedBy { it.index }.forEach { chunkInfo ->
                require(chunkInfo.file?.exists() == true) { "chunk file not exists: ${chunkInfo.name}" }
                require(chunkInfo.file.length() == chunkInfo.size) {
                    "chunk file size not match: ${chunkInfo.name}[${chunkInfo.size}, ${chunkInfo.file.length()}]"
                }
                // 验证MD5
                chunkInfo.file.inputStream().use { input ->
                    input.copyTo(output)
                }
            }
        }
        require(targetFile.md5() == manifest.md5) {
            "Target file doesn't match: ${manifest.md5}"
        }
        logger().info("Successfully merged chunk: ${targetFile.absolutePath}")
    }

    fun processManifest(manifestFile: String): ChunkManifest {
        val manifest = File(manifestFile)
        require(manifest.exists()) { "Manifest not exists" }
        return Gson().fromJson<ChunkManifest>(manifest.readText())
    }
}
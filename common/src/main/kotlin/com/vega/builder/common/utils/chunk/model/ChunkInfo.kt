package com.vega.builder.common.utils.chunk.model

import com.google.gson.annotations.SerializedName
import java.io.File

data class ChunkInfo(
    @SerializedName("index")
    val index: Int,         // 分块索引
    val name: String,       // 分块文件名
    val size: Long,         // 分块大小(字节)
    val md5: String,        // MD5哈希值
    val remoteKey: String,   // 远端存储key
    @Transient
    val file: File? = null,
)
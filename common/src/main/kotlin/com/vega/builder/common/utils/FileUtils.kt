package com.vega.builder.common.utils

import com.vega.builder.common.logger.logger
import org.apache.commons.io.FilenameUtils.removeExtension
import java.io.File
import java.security.MessageDigest
import java.util.Locale

private const val TAG = ""

fun File.printDirectoryTree(
    maxDepth: Int,
    level: Int = 0,
) {
    if (level == 0) {
        logger().info("DirTree[${this.absolutePath}]:")
    }
    if (!this.exists()) {
        println("File or directory does not exist.")
        return
    }

    val indent = "  ".repeat(level)

    if (this.isDirectory) {
        println("$indent[D]${this.name}")

        if (level < maxDepth) {
            val children = this.listFiles()
            if (children != null) {
                // 分离目录和文件
                val dirs = children.filter { it.isDirectory }
                val files = children.filter { it.isFile }

                // 先输出所有目录（无数量限制）
                dirs.forEach { child ->
                    child.printDirectoryTree(maxDepth, level + 1)
                }

                // 输出最多10个文件
                val maxFiles = 10
                files.take(maxFiles).forEach { file ->
                    println("$indent[F]${file.name}")
                }

                // 处理超限文件提示
                if (files.size > maxFiles) {
                    println("$indent  ... (${files.size - maxFiles} more files)")
                }
            }
        } else {
            println("$indent  ...") // 深度超限提示
        }
    } else {
        println("$indent${this.name}") // 文件处理
    }
}

fun File.md5(): String {
    val buffer = ByteArray(1024)
    val md5 = MessageDigest.getInstance("MD5")
    var numRead: Int

    inputStream().use { fis ->
        do {
            numRead = fis.read(buffer)
            if (numRead > 0) {
                md5.update(buffer, 0, numRead)
            }
        } while (numRead != -1)
    }

    val hashBytes = md5.digest()
    return hashBytes.joinToString("") { "%02x".format(it) }
}

fun withExtension(filePath: String, extension: String): String {
    if (filePath.lowercase(Locale.getDefault()).endsWith(extension)) {
        return filePath
    }
    return removeExtension(filePath) + extension
}

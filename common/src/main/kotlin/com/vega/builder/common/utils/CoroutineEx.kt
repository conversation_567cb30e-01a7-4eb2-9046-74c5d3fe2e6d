package com.vega.builder.common.utils

import com.vega.builder.common.logger.logger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.cancelChildren
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.selects.select
import java.util.concurrent.TimeUnit
import kotlin.coroutines.coroutineContext
import kotlin.time.Duration.Companion.milliseconds
import kotlinx.coroutines.channels.consumeEach
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

suspend inline fun <reified T> retry(
    times: Int,
    delayMillis: Long,
    block: (index: Int) -> T
): T {
    var currentAttempt = 0
    var lastError: Throwable? = null
    while (currentAttempt < times) {
        try {
            return block(currentAttempt)
        } catch (e: Throwable) {
            e.logger().error("retry:$currentAttempt", e)
            lastError = e
            currentAttempt++
            if (currentAttempt < times) {
                delay(delayMillis)
            }
        }
    }

    throw lastError ?: IllegalStateException("Unexpected error in retry function")
}

/**
 * 轮询结果状态（密封类）
 */
sealed class PollingResult<out T> {
    data class Success<T>(val result: T) : PollingResult<T>()
    data class Failure(val reason: Throwable) : PollingResult<Nothing>()
    data object Waiting : PollingResult<Nothing>()
    data object DoubleCheck : PollingResult<Nothing>()
}

/**
 * 通用轮询器
 *
 * @param T 轮询结果类型
 * @param initialDelay 首次轮询前的延迟（默认 0）
 * @param interval 轮询间隔时间
 * @param unit 时间单位（默认毫秒）
 * @param condition 轮询条件判断（返回非 null 表示成功并终止轮询）
 * @return 轮询结果（超时或取消时抛出异常）
 */

suspend fun <T> polling(
    initialDelay: Long = 0,
    interval: Long = 30,
    doubleCheck: Boolean = true,
    unit: TimeUnit = TimeUnit.SECONDS,
    condition: suspend () -> PollingResult<T>
): PollingResult<T> = coroutineScope {
    val intervalDuration = unit.toMillis(interval).milliseconds
    // 首次延迟
    if (initialDelay > 0) delay(unit.toMillis(initialDelay).milliseconds)
    // 轮询流程
    var checkTimes = 0
    while (true) {
        var result = condition()
        if (result is PollingResult.Failure && doubleCheck) {
            if (checkTimes == 0) {
                result = PollingResult.DoubleCheck
                checkTimes += 1
            }
        } else {
            checkTimes = 0
        }
        when (result) {
            is PollingResult.Success -> return@coroutineScope result
            is PollingResult.Failure -> return@coroutineScope result
            PollingResult.Waiting -> delay(intervalDuration)
            PollingResult.DoubleCheck -> delay(unit.toMillis(10).milliseconds)
        }
    }
    throw IllegalStateException("Unreachable code")
}


/**
 * 竞速执行多个任务，返回第一个成功结果
 * 全部失败时抛出异常，信息包含所有异常的message
 */
suspend fun <T> raceOfSuccess(vararg tasks: suspend () -> T): T {
    val results = Channel<T>(Channel.UNLIMITED)
    val exceptions = mutableListOf<Throwable>()

    val jobs = tasks.map { task ->
        CoroutineScope(coroutineContext).launch {
            try {
                val result = task()
                results.send(result)
            } catch (e: Exception) {
                synchronized(exceptions) {
                    exceptions.add(e)
                    // 如果是最后一个失败任务
                    if (exceptions.size == tasks.size) {
                        // 关闭通道并附加异常
                        results.close(Exception("exceptions[${exceptions.joinToString(",") { it.message ?: "no message" }}]"))
                    }
                }
            }
        }
    }

    return select {
        results.onReceiveCatching { result ->
            result.getOrThrow() // 成功结果
        }
    }.also {
        jobs.forEach { it.cancel("has job success") }
    }
}


/**
 * Current-limiting Channel
 * @param inputs 任务输入集合
 * @param block 任务执行函数（挂起函数）
 * @return 所有任务执行结果的列表（顺序与输入一致）
 */
suspend fun <InputType, ResultType> currentLimitChannel(
    inputs: List<InputType>,
    concurrency: Int = 3,
    block: suspend (InputType) -> ResultType
): List<ResultType> = coroutineScope {
    // 创建任务通道（无限容量确保发送不阻塞）
    val taskChannel = Channel< Pair<Int,InputType>>(Channel.UNLIMITED)
    val results = MutableList<ResultType?>(inputs.size) { null }
    val mutex = Mutex()

    // 启动工作协程
    val workers = (1..concurrency).map {
        launch(Dispatchers.IO) {
            for (input in taskChannel) {
                val result = block(input.second)
                mutex.withLock {
                    results[input.first] = result
                }
            }
        }
    }

    // 发送任务到通道
    inputs.forEachIndexed { index, item -> taskChannel.send(index to item) }
    taskChannel.close()

    // 等待所有任务完成
    workers.joinAll()
    results.filterNotNull() // 安全转换为非空列表
}
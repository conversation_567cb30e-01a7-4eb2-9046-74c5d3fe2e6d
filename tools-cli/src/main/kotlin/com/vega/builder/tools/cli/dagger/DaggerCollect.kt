package com.vega.builder.tools.cli.dagger

import com.vega.builder.common.psi.asttools.KotlinParserUtil
import com.vega.builder.common.psi.matching.match
import org.jetbrains.kotlin.psi.KtAnnotated
import org.jetbrains.kotlin.psi.KtAnnotationEntry
import org.jetbrains.kotlin.psi.KtClass
import org.jetbrains.kotlin.psi.KtFile
import org.jetbrains.kotlin.psi.KtModifierList
import org.jetbrains.kotlin.psi.KtNamedFunction
import org.jetbrains.kotlin.psi.KtParameter
import org.jetbrains.kotlin.psi.KtPrimaryConstructor
import org.jetbrains.kotlin.psi.KtProperty
import org.jetbrains.kotlin.psi.KtTypeReference
import org.w3c.dom.Document
import java.io.File
import java.nio.file.Path
import javax.xml.parsers.DocumentBuilderFactory
import kotlin.io.path.Path
import kotlin.io.path.absolutePathString

object DaggerCollector {
    val interfaceBlackList =
        listOf(
            "CoroutineScope",
            "ViewModel",
            "IAbility",
            "DisposableViewModel",
            "BaseMviViewModel",
            "ViewModelProvider.Factory"
        )

    fun collect(rootPath: String): List<ModuleInfo> {
        val result = ModuleScanner.scanProject(Path(rootPath))
        val moduleInfoList = mutableListOf<ModuleInfo>()
        for (moduleFiles in result) {

            val moduleClassDaggerInfo = moduleFiles.kotlinFiles.map {
                val modelInfoList = mutableListOf<ModelInfo>()
                val classInfoList = mutableListOf<ClassInfo>()
                val bindInfoList = mutableListOf<BindInfo>()
                val viewModelBindInfoList = mutableListOf<BindInfo>()
                val providerList = mutableListOf<ProvidersInfo>()

                val ktFile = KotlinParserUtil.parseAsFile(it.path.readText(), it.path.absolutePath)
                val allClasses = ktFile.findAllClasses()


                for (ktClass in allClasses) {
                    //存在被注入的情况
                    ktClass.collectDaggerModel().let {
                        it.first?.let { modelInfo ->
                            modelInfoList.add(modelInfo)
                        }
                        it.second?.let { classInfo ->
                            classInfoList.add(classInfo)
                        }
                    }

                    val isDaggerModule = ktClass.isDaggerModule()
                    if (isDaggerModule) {
                        val bindList = ktClass.collectBindList()
                        bindInfoList.addAll(bindList.mapToBindInfo())
                        val viewModelBindList = ktClass.collectViewModelBindList()
                        viewModelBindInfoList.addAll(viewModelBindList.mapToBindInfo())
                        val providersList = ktClass.collectProviders()
                        providersList.map { provider ->
                            val isSingleton = provider.annotationEntries.any { it.text == "@Singleton" }
                            val isActivityScope = provider.annotationEntries.any { it.text == "@ActivityScope" }

                            providerList.add(
                                ProvidersInfo(

                                    TypeInfo(ktClass.containingKtFile.packageFqName.asString(), ktClass.name!!),
                                    provider.name!!,
                                    provider.typeReference!!.findFullName(),
                                    when {
                                        isSingleton -> DaggerScope.Singleton
                                        isActivityScope -> DaggerScope.Activity
                                        else -> DaggerScope.None
                                    },
                                    provider.valueParameters.size,
                                    paramsInfoList = provider.valueParameters.mapIndexed { index, parameter ->
                                        parameter.collectModelParamsInfo(
                                            index
                                        )
                                    }.filterNotNull()
                                )
                            )
                        }
                    }

                }
                FileInfo(
                    it.path,
                    it.flavor,
                    moduleFiles.modulePath,
                    modelInfoList,
                    classInfoList,
                    bindInfoList,
                    viewModelBindInfoList,
                    providerList
                )
            }.filter {
                it.modelInfoList.isNotEmpty()
                        || it.classInfoList.isNotEmpty()
                        || it.bindInfoList.isNotEmpty()
                        || it.viewModelBindInfoList.isNotEmpty()
                        || it.providerList.isNotEmpty()
            }

            val koinModelNameCount = moduleFiles.modulePath.name.replaceFirst("lib", "").split("-").joinToString {
                it.replaceFirstChar { it.uppercaseChar() }
            }
            val koinModuleName = "Koin${koinModelNameCount}Module.kt"

            if (moduleClassDaggerInfo.isNotEmpty()) {
                moduleInfoList.add(
                    ModuleInfo(
                        moduleFiles.modulePath,
                        moduleClassDaggerInfo,
                        moduleFiles.packageNames,
                        koinFileMap = moduleFiles.kotlinFiles.filter { it.path.name == koinModuleName }.associate {
                            it.flavor to it.path.absolutePath
                        }
                    )
                )
            }
        }
        return moduleInfoList
    }

    fun KtClass.collectBindList(): List<KtNamedFunction> {
        return match<KtNamedFunction>().apply {
            addCustomMatcher { it.annotationEntries.any { it.text == "@Binds" } }
            addCustomMatcher { it.annotationEntries.none { it.text == "@IntoMap" } }
        }.findAll(this)
    }

    fun KtClass.collectViewModelBindList(): List<KtNamedFunction> {
        return match<KtNamedFunction>().apply {
            addCustomMatcher { it.annotationEntries.any { it.text == "@Binds" } }
            addCustomMatcher { it.annotationEntries.any { it.text == "@IntoMap" } }
        }.findAll(this)
    }

    fun List<KtNamedFunction>.mapToBindInfo(): List<BindInfo> {
        return mapNotNull {
            val isSingleton = it.annotationEntries.any { it.text == "@Singleton" }
            val isActivityScope = it.annotationEntries.any { it.text == "@ActivityScope" }
            val className = it.valueParameters.first().typeReference?.findFullName() //
            val interfaceName = it.typeReference?.findFullName()
            if (interfaceName != null && className != null) {
                BindInfo(
                    className, interfaceName, when {
                        isSingleton -> DaggerScope.Singleton
                        isActivityScope -> DaggerScope.Activity
                        else -> DaggerScope.None
                    }
                )
            } else {
                null
            }
        }
    }

    /**
     * Providers 类型的绑定需要手动处理
     */
    fun KtClass.collectProviders(): List<KtNamedFunction> {
        return match<KtNamedFunction>().apply {
            addCustomMatcher { it.annotationEntries.any { it.text == "@Provides" } }
        }.findAll(this)
    }

    fun KtTypeReference.findFullName(): TypeInfo {
        val typeName = text

        val packageName = containingKtFile.importDirectives.map { it.importedFqName?.asString() }
            .find { it?.split(".")?.last() == typeName } ?: containingKtFile.packageFqName.asString()

//        ktFile?.importDirectives?.
        return TypeInfo(packageName.split(".").filter { typeName != it }.joinToString("."), typeName)
    }

    fun KtClass.collectDaggerModel(): Pair<ModelInfo?, ClassInfo?> {
        val hasDaggerPropertyInject = hasDaggerPropertyInject()
        //存在构造函数注入的情况
        val hasDaggerPrimaryConstructorInject = hasDaggerPrimaryConstructorInject()
        var modelInfo: ModelInfo? = null
        var classInfo: ClassInfo? = null


        val packageName = containingKtFile.packageFqName.asString()
        if (hasDaggerPrimaryConstructorInject) {
            //是Model也是被注入的
            val isSingleton = hasSingleton()
            val isActivityScope = hasActivityScope()
            modelInfo = ModelInfo(
                TypeInfo(packageName, name!!),
                when {
                    isSingleton -> DaggerScope.Singleton
                    isActivityScope -> DaggerScope.Activity
                    else -> DaggerScope.None
                },
                parameterCount = primaryConstructorParameters.size,
                params = primaryConstructorParameters.mapIndexed { index, parameter ->
                    parameter.collectModelParamsInfo(
                        index
                    )
                }.filterNotNull(),
                interfaces = superTypeListEntries
                    .filter { it.typeReference?.typeElement?.text !in interfaceBlackList }
                    .mapNotNull { it.typeReference?.findFullName() }
            )
        }
        if (hasDaggerPropertyInject || hasDaggerPrimaryConstructorInject) {
            classInfo =
                ClassInfo(TypeInfo(packageName, name!!), hasDaggerPropertyInject, hasDaggerPrimaryConstructorInject)

        }
        return modelInfo to classInfo
    }

    fun KtParameter.collectModelParamsInfo(index: Int): ParamsInfo? {
        return typeReference?.let { typeReference ->
            if (typeReference.text.startsWith("Provider")) {
                typeReference.typeElement?.typeArgumentsAsTypes?.first()?.let { type ->
                    ParamsInfo(index, type.findFullName(), true, false)
                }
            } else {
                ParamsInfo(index, typeReference.findFullName(), false, typeReference.text.startsWith("Scope"))
            }
        }
    }

    fun KtClass.hasDaggerPropertyInject(): Boolean {
        val results =
            match<KtProperty>()
                .apply {
                    addCustomMatcher { it.valOrVarKeyword.text == "var" }
                    addCustomMatcher {
                        it.annotationEntries.any { it.text == "@Inject" }
                    }

                    addCustomMatcher { !it.typeReference?.text.isNullOrBlank() }
                }
                .findAll(this)
        return results.isNotEmpty()
    }

    fun KtClass.hasDaggerPrimaryConstructorInject(): Boolean {
        return match<KtAnnotationEntry>().apply {
            addCustomMatcher { it.text == "@Inject" }
            addCustomMatcher { findAnnotationTarget(it) is KtPrimaryConstructor }
        }.findAll(this).isNotEmpty()
    }

    fun KtClass.hasSingleton(): Boolean {
        return match<KtAnnotationEntry>().apply {
            addCustomMatcher { it.text == "@Singleton" }
            addCustomMatcher { findAnnotationTarget(it) is KtClass }
        }.findAll(this).isNotEmpty()
    }

    fun KtClass.hasActivityScope(): Boolean {
        return match<KtAnnotationEntry>().apply {
            addCustomMatcher { it.text == "@ActivityScope" }
            addCustomMatcher { findAnnotationTarget(it) is KtClass }
        }.findAll(this).isNotEmpty()
    }

    fun KtFile.findAllClasses(): List<KtClass> {
        return match<KtClass>().findAll(this)
    }

    fun KtClass.isDaggerModule(): Boolean {
        return match<KtAnnotationEntry>().apply {
            addCustomMatcher { it.typeReference?.text == "Module" }
            addCustomMatcher { findAnnotationTarget(it) is KtClass }
        }.findAll(this).isNotEmpty()
    }

    fun findAnnotationTarget(annotation: KtAnnotationEntry): KtAnnotated? {
        var parent = annotation.parent
        while (parent != null) {
            when (parent) {
                is KtModifierList -> {
                    val annotatedElement = parent.parent
                    if (annotatedElement is KtAnnotated) {
                        return annotatedElement
                    }
                }

                is KtFile -> {
                    // 文件级注解直接返回 KtFile
                    return parent
                }
            }
            parent = parent.parent
        }
        return null
    }

}

fun parsePackageName(moduleFile: File, manifestFile: File): String {

    val factory = DocumentBuilderFactory.newInstance()
    val builder = factory.newDocumentBuilder()
    val doc: Document = builder.parse(manifestFile)
    doc.documentElement.normalize()

    // 直接获取 manifest 节点的 package 属性
    return doc.documentElement.getAttribute("package").takeIf { it.isNotEmpty() }
        ?: "com.vega.${moduleFile.name
            .replaceFirst("lib", "")
            .replace('.', ' ')
            .replace("lv-","")
            .replace("-","")
            .lowercase()
            
        }"

}


/**模块
 *
 */
data class ModuleInfo(
    val modulePath: File,
    val kotlinFiles: List<FileInfo>,
    val packageNames: Map<String, String>,
    val koinFileMap: Map<String, String> = emptyMap()
) {
    fun packageName(flavor: String): String {
        val flavorPackageName = packageNames[flavor]
        return if (flavorPackageName.isNullOrBlank()) {
            packageNames["main"] ?: "com.vega.lv"
        } else {
            flavorPackageName
        }
    }
}

/**
 * Dagger相关文件
 */
data class FileInfo(
    val path: File,
    val flavor: String,
    val parentModule: File,
    val modelInfoList: List<ModelInfo>,
    val classInfoList: List<ClassInfo>,
    val bindInfoList: List<BindInfo>,
    val viewModelBindInfoList: List<BindInfo>,
    val providerList: List<ProvidersInfo>
)

/**
 * 类型信息
 */
data class TypeInfo(
    val _packageName: String?,
    val _typeName: String
) {

    val packageName: String?
        get() = if (_typeName.startsWith("com.")) {
            ""
        } else {
            _packageName
        }
    val typeName: String
        get() = fullName().split('.').last()

    fun fullName(): String {
        return if (packageName?.isNotEmpty() == true) {
            "$packageName.$_typeName"
        } else {
            _typeName
        }
    }
}

/**
 * 绑定信息
 */
data class BindInfo(val className: TypeInfo, val interfaceName: TypeInfo, val daggerScope: DaggerScope)

data class ProvidersInfo(
    val className: TypeInfo,
    val providerFunction: String,
    val returnType: TypeInfo,
    var daggerScope: DaggerScope,
    val paramsCount: Int = 0,
    val paramsInfoList: List<ParamsInfo> = emptyList(),
    var hasScopeChile: Boolean = false,

    )

/**
 * 文件中在Dagger中注册的模块
 */
data class ModelInfo(
    val name: TypeInfo,
    var daggerScope: DaggerScope,
    val parameterCount: Int = 0,
    val params: List<ParamsInfo> = emptyList(),
    var hasScopeChile: Boolean = false,
    val interfaces: List<TypeInfo> = emptyList(),
)

data class ParamsInfo(
    val index: Int,
    val type: TypeInfo,
    val isProvider: Boolean,
    val isScopeInstance: Boolean,
)

/**
 * 文件中包含的类的信息
 */
data class ClassInfo(
    val name: TypeInfo,
    val hasDaggerPropertyInject: Boolean,
    val hasDaggerPrimaryConstructorInject: Boolean,
)

enum class DaggerScope(val func: String, val ofFunc: String) {
    Singleton("single", "singleOf"),
    Activity("scoped", "scopedOf"),
    None("factory", "factoryOf")
}


/**
 * 文件操作工具类，封装 Gradle 模块扫描相关功能
 */
object ModuleScanner {

    // 核心数据结构：模块扫描结果
    data class ScanResult(
        val modulePath: File,
        val kotlinFiles: List<FlavorFileInfo>,
        val fileCount: Int,
        val packageNames: Map<String, String>,
    )

    data class FlavorFileInfo(
        val path: File,
        val flavor: String,
    )

    /**
     * 执行完整扫描流程
     */
    fun scanProject(root: Path): List<ScanResult> {
        val modulesFile = root.toFile().collectModules().filter {  "modules/retouch_sdk" !in it.absolutePath }
        return modulesFile.map { moduleFile ->
            val files = moduleFile.collectFiles()
            val kotlinFiles = files.filter { it.isKotlinFile() }
            val androidManifestFiles =
                files.filter { it.isAndroidManifest() }.filter { !it.toRelativeString(moduleFile).startsWith("build") }
                    .map(::toFlavorFileInfo)


            ScanResult(
                modulePath = moduleFile,
                kotlinFiles = kotlinFiles.map(::toFlavorFileInfo),
                fileCount = kotlinFiles.size,
                packageNames = androidManifestFiles.associate {
                    it.flavor to parsePackageName(moduleFile, it.path).replaceFirst("com.lemon", "com.vega")
                }
            )
        }.filter { it.fileCount > 0 }
    }

    fun toFlavorFileInfo(file: File): FlavorFileInfo {
        var parentFile = file
        var currentFile = file
        do {
            if (parentFile.name == "src") {
                return FlavorFileInfo(
                    path = file,
                    flavor = currentFile.name
                )
            }
            currentFile = parentFile
            parentFile = parentFile.parentFile
        } while (parentFile.parentFile != null)
        return FlavorFileInfo(file, "main")
    }


    fun File.collectFiles(): List<File> {
        val subModules = collectModules()
        val result = this.walkTopDown()
            .filter { file ->
                subModules.none { file.toPath().startsWith(it.toPath()) }
            }
            .filter { it.isKotlinFile() || it.isAndroidManifest() }
            .toList()
        return result
    }

    fun File.collectModules(): List<File> {
        return walkTopDown().filter { it.isDirectory() && it.isGradleModule() }.toList()
            .filter { it.absolutePath != absolutePath }
    }


    /****************** 扩展函数 ******************/

    /**
     * 判断是否是 Gradle 模块目录
     */
    private fun File.isGradleModule(): Boolean =
        resolve("build.gradle").exists() ||
                resolve("build.gradle.kts").exists()

    /**
     * 判断是否是 Kotlin 文件
     */
    private fun File.isKotlinFile(): Boolean =
        !isDirectory && name.endsWith(".kt")

    private fun File.isAndroidManifest(): Boolean =
        !isDirectory && name == "AndroidManifest.xml"

}

/****************** 使用示例 ******************/


private fun printResults(results: List<ModuleScanner.ScanResult>) {
    val json = buildString {
        appendLine("{")
        appendLine("  \"modules\": {")

        results.joinTo(this, ",\n") { result ->
            buildString {
                append("    \"${result.modulePath}\": {")
                append("\"path\": \"${result.modulePath}\", ")
                append("\"kotlin_files\": [")
                append(result.kotlinFiles.joinToString(", ") { "\"${it.path.toPath().absolutePathString()}\"" })
                append("], ")
                append("\"file_count\": ${result.fileCount}}")
            }
        }

        appendLine("\n  }")
        appendLine("}")
    }

    println(json)
}

fun main() {

    DaggerCollector.apply {
        val file =
            File("/Users/<USER>/develop/bytedance/vega/modules/vega_base/libeffect/src/main/java/com/vega/libeffect/di/EffectModule.kt")
        val ktFile = KotlinParserUtil.parseAsFile(file.readText(), file.absolutePath)
        val allClasses = ktFile.findAllClasses()
        for (clazz in allClasses) {
            val bind = clazz.collectBindList()
            val result = match<KtAnnotationEntry>().apply {
                addCustomMatcher {
                    it.typeReference?.text == "Module"
                }
                addCustomMatcher { findAnnotationTarget(it) is KtClass }
            }.findAll(clazz).isNotEmpty()
            println(bind)
        }

    }
}
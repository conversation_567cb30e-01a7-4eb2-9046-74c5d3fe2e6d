type: pipeline
jobs:
  #  先编译国内版
  cli-publish:
    type: service
    service_name: build
    display_name: 测试流水线发布
    settings:
      template_config_id: 67969  # 编译配置id
      publish_env: test
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"
  build-prod:
    type: service
    service_name: build
    display_name: 国内包大小分支包
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    settings:
      #      skip_package: true
      template_config_id: 67983  # 编译配置id
      DEFAULT_TEMPLATE: MR_BUILD_LV
      MAIN_GIT_URL: ******************:faceu-android/vega.git
      MAIN_GIT_BRANCH: develop
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"

  build-oversea:
    type: service
    service_name: build
    display_name: 海外包大小分支包
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    settings:
      template_config_id: 67983  # 编译配置id
      DEFAULT_TEMPLATE: MR_BUILD_CC
      MAIN_GIT_BRANCH: develop
      MAIN_GIT_URL: ******************:faceu-android/cc.git

    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"

  build-oversea-32:
    type: service
    service_name: build
    display_name: 海外32位包
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    settings:
      template_config_id: 67983  # 编译配置id
      package_type: compare
      DEFAULT_TEMPLATE: MR_BUILD_CC_32
      MAIN_GIT_BRANCH: develop
      MAIN_GIT_URL: ******************:faceu-android/cc.git
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"

  build-oversea-us-32:
    type: service
    service_name: build
    display_name: 海外US32位包
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    settings:
      template_config_id: 67983  # 编译配置id
      package_type: compare
      DEFAULT_TEMPLATE: MR_BUILD_CC
      MAIN_GIT_BRANCH: develop
      MAIN_GIT_URL: ******************:faceu-android/cc.git
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"
    # 国内覆盖率包编译
  build-commercepro-coverage:
    type: service
    service_name: build
    display_name: 新营销覆盖率包
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"
    settings:
      template_config_id: 67983  # 编译配置id
      DEFAULT_TEMPLATE: MR_COVERAGE_COMMERCEPRO_PROD
      MAIN_GIT_BRANCH: commercepro/develop
      MAIN_GIT_URL: ******************:faceu-android/vega.git

  # 国内覆盖率包编译
  build-prod-coverage:
    type: service
    service_name: build
    display_name: 国内覆盖率包
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"
    settings:
      template_config_id: 67983  # 编译配置id
      DEFAULT_TEMPLATE: MR_COVERAGE_LV
      MAIN_GIT_BRANCH: develop
      MAIN_GIT_URL: ******************:faceu-android/vega.git


  # 海外覆盖率包编译
  build-oversea-coverage:
    type: service
    service_name: build
    display_name: 海外覆盖率包
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"
    settings:
      template_config_id: 67983  # 编译配置id
      DEFAULT_TEMPLATE: MR_COVERAGE_CC
      MAIN_GIT_BRANCH: develop
      MAIN_GIT_URL: ******************:faceu-android/cc.git

  # 国内覆盖率包编译
  build-tinycut-coverage:
    type: service
    service_name: build
    display_name: TinyCut覆盖率包
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"
    settings:
      template_config_id: 67983  # 编译配置id
      DEFAULT_TEMPLATE: MR_COVERAGE_TC
      MAIN_GIT_BRANCH: tinycut/develop
      MAIN_GIT_URL: ******************:faceu-android/vega.git

  build-prod-debug:
    type: service
    service_name: build
    display_name: 剪映Debug
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    settings:
      template_config_id: 67983  # 编译配置id
      DEFAULT_TEMPLATE: MR_BUILD_DEBUG_LV
      MAIN_GIT_BRANCH: develop
      MAIN_GIT_URL: ******************:faceu-android/vega.git
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"
  build-oversea-debug:
    type: service
    service_name: build
    display_name: CapCut Debug
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    settings:
      template_config_id: 67983  # 编译配置id
      DEFAULT_TEMPLATE: MR_BUILD_DEBUG_CC
      MAIN_GIT_BRANCH: develop
      MAIN_GIT_URL: ******************:faceu-android/cc.git
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"
  publish-sub-repo-oversea:
    type: service
    service_name: build
    display_name: 发布子仓二进制-oversea
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    settings:
      MAIN_GIT_URL: ******************:faceu-android/cc.git
      template_config_id: 67983  # 编译配置id
      MAIN_GIT_BRANCH: develop
      BUILD_PARAMS: "{\"RUNNING_TYPE\":\"publish_sub_repo\",\"PUBLISH_FLAVOR\":\"oversea\",\"IS_OVERSEA\":\"true\"}"
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"
  publish-sub-repo-prod:
    type: service
    service_name: build
    display_name: 发布子仓二进制-prod
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    settings:
      MAIN_GIT_URL: ******************:faceu-android/vega.git
      template_config_id: 67983  # 编译配置id
      MAIN_GIT_BRANCH: develop
      BUILD_PARAMS: "{\"RUNNING_TYPE\":\"publish_sub_repo\",\"PUBLISH_FLAVOR\":\"prod\"}"
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"
  cd-cc:
    type: service
    service_name: build
    display_name: CapCut CD构建
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    settings:
      template_config_id: 67983  # 编译配置id
      DEFAULT_TEMPLATE: OUT_CHANNEL_CC_GOOGLEPLAY
      MAIN_GIT_BRANCH: develop
      MAIN_GIT_URL: ******************:faceu-android/cc.git
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"
  cd-lv:
    type: service
    service_name: build
    display_name: 剪映 CD构建
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    settings:
      template_config_id: 67983  # 编译配置id
      DEFAULT_TEMPLATE: OUT_CHANNEL_LV
      MAIN_GIT_BRANCH: develop
      MAIN_GIT_URL: ******************:faceu-android/vega.git
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"
  cd-lv-mac:
    type: service
    service_name: build
    display_name: 剪映 CD构建 Mac
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    settings:
      template_config_id: 80513  # 编译配置id
      DEFAULT_TEMPLATE: OUT_CHANNEL_LV
      MAIN_GIT_BRANCH: develop
      MAIN_GIT_URL: ******************:faceu-android/vega.git
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"

  cd-cc-mac:
    type: service
    service_name: build
    display_name: CapCut CD构建 Mac
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    settings:
      template_config_id: 79117  # 编译配置id
      DEFAULT_TEMPLATE: OUT_CHANNEL_CC_GOOGLEPLAY
      MAIN_GIT_BRANCH: develop
      MAIN_GIT_URL: ******************:faceu-android/cc.git
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"
  benchmark:
    type: service
    service_name: build
    display_name: benchmark
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    settings:
      template_config_id: 67983  # 编译配置id
      BUILD_PARAMS: "{\"RUNNING_TYPE\":\"benchmark\",\"BENCHMARK_TYPE\":\"lv_incremental_build_abi\"}"
      MAIN_GIT_BRANCH: develop
      MAIN_GIT_URL: ******************:faceu-android/vega.git
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"
  ttp-version-checkout:
    type: service
    service_name: build
    display_name: TT Version Check
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    settings:
      template_config_id: 67983  # 编译配置id
      BUILD_PARAMS: "{\"RUNNING_TYPE\":\"check_ttp_version\"}"
      MAIN_GIT_BRANCH: develop
      MAIN_GIT_URL: ******************:faceu-android/cc.git
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"
  ttp-build-gp:
    type: service
    service_name: build
    display_name: ttp channel(GP) build
    depends_on:
      - cli-publish
    failed_approvers:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    settings:
      template_config_id: 67983  # 编译配置id
      BUILD_PARAMS: "{\"RUNNING_TYPE\":\"ttp_build_wrapper\",\"TTP_BUILD_SUCCESS_VERSION\":\"apk-build-1754621235879\"}"
      DEFAULT_TEMPLATE: OUT_CHANNEL_CC_GOOGLEPLAY
      MAIN_GIT_BRANCH: overseas/release/14.9.0
      MAIN_GIT_URL: ******************:faceu-android/cc.git
    except:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"
  cli-release-publish:
    type: service
    service_name: build
    display_name: 流水线正式版发布
    settings:
      template_config_id: 67969  # 编译配置id
      publish_env: prod
    only:
      variables:
        - $AFTER_CHECK_PIPELINE == "1"
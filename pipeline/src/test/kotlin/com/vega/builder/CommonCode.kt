package com.vega.builder

import java.io.File
import kotlin.test.Test


class CommonCode {
    @Test
    fun test() {

        val projectRoot = File("/Users/<USER>/develop/bytedance/cc") // 当前目录作为项目根目录
        val gradleFiles = findGradleFiles(projectRoot)

        if (gradleFiles.isEmpty()) {
            println("未找到build.gradle文件")
            return
        }

        val duplicatePlugins = findDuplicatePlugins(gradleFiles)

        if (duplicatePlugins.isEmpty()) {
            println("未检测到重复的插件声明")
        } else {
            printDuplicateReport(duplicatePlugins)
        }
    }

    fun findGradleFiles(directory: File): List<File> {
        return directory.walk()
            .filter { it.isFile }
            .filter { it.name == "build.gradle" || it.name == "build.gradle.kts" }
            .toList()
    }

    fun findDuplicatePlugins(files: List<File>): Map<File, Map<String, List<Int>>> {
        val results = mutableMapOf<File, Map<String, List<Int>>>()

        files.forEach { file ->
            val pluginOccurrences = mutableMapOf<String, MutableList<Int>>()
            var lineNumber = 0

            file.useLines { lines ->
                lines.forEach { line ->
                    lineNumber++
                    // 匹配 apply plugin: 'xxx' 或 apply plugin: "xxx"
                    val pluginName = extractPluginName(line)
                    if (pluginName != null) {
                        pluginOccurrences
                            .getOrPut(pluginName) { mutableListOf() }
                            .add(lineNumber)
                    }
                }
            }

            // 只保留有重复的记录
            val duplicates = pluginOccurrences.filter { it.value.size > 1 }
            if (duplicates.isNotEmpty()) {
                results[file] = duplicates
            }
        }

        return results
    }

    fun extractPluginName(line: String): String? {
        val regex = """apply\s+plugin\s*:\s*['"]([^'"]+)['"]""".toRegex(RegexOption.IGNORE_CASE)
        val match = regex.find(line.trim()) ?: return null
        return match.groupValues[1]
    }

    fun printDuplicateReport(duplicates: Map<File, Map<String, List<Int>>>) {
        duplicates.forEach { (file, plugins) ->
            println("\n文件: ${file.absolutePath}")
            plugins.forEach { (plugin, lines) ->
                println("  插件: '$plugin'")
                println("  重复行号: ${lines.joinToString(", ")}")
                println("  声明次数: ${lines.size}")
            }
        }
        println("\n总重复文件数: ${duplicates.size}")
    }
}
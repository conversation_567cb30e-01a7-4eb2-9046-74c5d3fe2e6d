package com.vega.builder.pipeline.task.prepare

import com.vega.builder.common.logger.configureLogback
import com.vega.builder.common.throwable.PipelineThrowableHandler
import com.vega.builder.common.utils.printHello
import com.vega.builder.common.utils.setenvMock
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.di.configureKoin
import com.vega.builder.pipeline.main
import com.vega.builder.pipeline.task.clone.GitInitializeTask
import kotlinx.coroutines.runBlocking
import org.koin.mp.KoinPlatform
import kotlin.test.Test

class TestPrepareFileTask {

    fun mockEnv() {
        setenvMock("SYSTEM_MOCK", "true")
        setenvMock("WORKSPACE", System.getProperty("user.home"))
        setenvMock("MAIN_GIT_URL", "******************:faceu-android/vega.git")
        setenvMock("MAIN_GIT_BRANCH", "dreamina/feature/oversea-0715")
        setenvMock("DEFAULT_TEMPLATE", "MR_BUILD_DREAMINA_OVERSEAS")
    }
    @Test
    fun `test prepare network file`() {
        mockEnv()
        PipelineThrowableHandler.register()
        printHello()
        configureLogback()
        configureKoin()
        runBlocking {
            KoinPlatform.getKoin().get<BuildParams>().loadParams()
            GitInitializeTask().execute()
            PrepareFileTask().execute()
        }

    }
}
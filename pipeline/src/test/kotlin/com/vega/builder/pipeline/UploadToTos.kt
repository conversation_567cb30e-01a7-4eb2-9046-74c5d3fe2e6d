package com.vega.builder.pipeline

import com.vage.builder.source.manager.core.SourceManager
import com.vega.builder.common.utils.setenvMock
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class UploadToTos {

    fun mockEnv() {
        setenvMock("SYSTEM_MOCK", "true")
        setenvMock("WORKSPACE", System.getProperty("user.home"))
        setenvMock("MAIN_GIT_URL", "******************:faceu-android/vega.git")
        setenvMock("MAIN_GIT_BRANCH", "develop")
        setenvMock("MAIN_GIT_COMMIT", "")
//        setenvMock("TARGET_BRANCH", "feat/tanhaiyang/merge_test_target1")
//        setenvMock("MERGE_TARGET", "true")
    }

    @Test
    fun clearRepoTask() {
        mockEnv()
        main()
    }
}
package com.vega.builder.pipeline.task.conflict

import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

/**
 * Test for PodfileSeerProcessor
 */
class PodfileSeerProcessorTest : BaseProcessorTest() {

    override val filePath: String = "Podfile.seer"

    override var fileContent = """
        - AALVLaunchTracker (path 'Modules/AALVLaunchTracker')
        - ABRInterface (2.4.0, from '******************:iOS_Library/privatethird_binary_repo.git')
        <<<<<<< HEAD
        - ABRModule (2.4.0, from '******************:iOS_Library/privatethird_binary_repo.git')
        =======
        - ABRModule (2.5.1, from '******************:iOS_Library/privatethird_binary_repo.git')
        >>>>>>> feature-branch
        - ABUAdCsjAdapter (6.0.0, from '******************:ad/ad_union_source_repo.git')
        <<<<<<< HEAD
        - ADFeelGood (2.1.16, from '******************:iOS_Library/privatethird_binary_repo.git')
        =======
        - ADFeelGood (2.0.5, from '******************:iOS_Library/privatethird_binary_repo.git')
        >>>>>>> feature-branch
        - AFgzipRequestSerializer (0.0.6, from '******************:iOS_Library/privatethird_source_repo.git')
        - AFNetworking (2.7.0, from '******************:iOS_Library/publicthird_binary_repo.git')
    """.trimIndent()

    override val resolveConflict = """
        - AALVLaunchTracker (path 'Modules/AALVLaunchTracker')
        - ABRInterface (2.4.0, from '******************:iOS_Library/privatethird_binary_repo.git')
        - ABRModule (2.5.1, from '******************:iOS_Library/privatethird_binary_repo.git')
        - ABUAdCsjAdapter (6.0.0, from '******************:ad/ad_union_source_repo.git')
        - ADFeelGood (2.1.16, from '******************:iOS_Library/privatethird_binary_repo.git')
        - AFgzipRequestSerializer (0.0.6, from '******************:iOS_Library/privatethird_source_repo.git')
        - AFNetworking (2.7.0, from '******************:iOS_Library/publicthird_binary_repo.git')
    """.trimIndent()

    private val threeWayConflictContent = """
        - SomeOtherPod (1.0.0)
        <<<<<<<
        - LVVideoEditor (**********-cc, from '******************:iOS_Library/lv-ios_source_repo.git')
        |||||||
        - LVVideoEditor (*********-cc, from '******************:iOS_Library/lv-ios_source_repo.git')
        =======
        - LVVideoEditor (*********-cc, from '******************:iOS_Library/lv-ios_source_repo.git')
        >>>>>>> feature-branch
        - AnotherPod (2.0.0)
    """.trimIndent()

    private val threeWayResolveConflict = """
        - SomeOtherPod (1.0.0)
        - LVVideoEditor (*********-cc, from '******************:iOS_Library/lv-ios_source_repo.git')
        - AnotherPod (2.0.0)
    """.trimIndent()

    @Test
    fun testProcessConflictBlock() {
        val mockFile = mockFile()
        val mockFileReader = mockFileReader()
        val processor = PodfileSeerProcessor(mockFile, mockFileReader)
        runBlocking {
            processor.process()
        }
        Assertions.assertEquals(resolveConflict, processor.getContent())
    }

    @Test
    fun testProcessThreeWayConflict() {
        // 替换测试内容为三方冲突场景
        this.fileContent = threeWayConflictContent
        val mockFile = mockFile()
        val mockFileReader = mockFileReader()
        val processor = PodfileSeerProcessor(mockFile, mockFileReader)

        runBlocking {
            processor.process()
        }

        // 验证处理结果是否选择了最新版本
        Assertions.assertEquals(threeWayResolveConflict, processor.getContent())
    }

    @Test
    fun testVersionExtraction() {
        val processor = PodfileSeerProcessor("test.seer")

        val extractVersionMethod = processor.javaClass.getDeclaredMethod("extractSimpleVersion", String::class.java)
        extractVersionMethod.isAccessible = true

        val testCases = mapOf(
            "- ABRModule (2.4.0, from '******************:iOS_Library/privatethird_binary_repo.git')" to "2.4.0",
            "- ADFeelGood (2.1.16, from '******************:iOS_Library/privatethird_binary_repo.git')" to "2.1.16",
            "- Ads-CN (6.4.4, from '******************:ad/ad_union_source_repo.git')" to "6.4.4",
            "- ByteDanceKit (git '******************:ugc/ByteDanceKit.git', commit 'd993a91d42c29fda3ee32fe1cedf54f46ec08f72')" to "",
            "- AALVLaunchTracker (path 'Modules/AALVLaunchTracker')" to "",
            "# This is a comment" to "",
            "- LVVideoEditor (**********-cc, from '******************:iOS_Library/lv-ios_source_repo.git')" to "**********",
            "- LVVideoEditor (*********-cc, from '******************:iOS_Library/lv-ios_source_repo.git')" to "*********",
            "- LVVideoEditor (*********-cc, from '******************:iOS_Library/lv-ios_source_repo.git')" to "*********"
        )

        testCases.forEach { (input, expected) ->
            val result = extractVersionMethod.invoke(processor, input) as String
            Assertions.assertEquals(expected, result, "Failed for input: $input")
        }
    }
}
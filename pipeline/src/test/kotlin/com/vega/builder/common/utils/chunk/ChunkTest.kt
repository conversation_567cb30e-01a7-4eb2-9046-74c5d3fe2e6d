package com.vega.builder.common.utils.chunk

import java.io.RandomAccessFile
import java.util.Random
import kotlin.io.path.createTempDirectory
import kotlin.io.path.createTempFile
import kotlin.test.Test

class ChunkTest {
    @Test
    fun testSplitFile() {
        val testFile = createTempFile(prefix = "test_source", suffix = ".bin").toFile()
        generateRandomFile(testFile.absolutePath, 1024 * 1024 * 1024L)
        val targetDir = createTempDirectory("test_chunk").toFile()
        val splitResult = ChunkSplitter.splitFile(testFile.absolutePath, targetDir.absolutePath, "test_key")
        val manifest = ChunkMerger.processManifest(splitResult.first.absolutePath)

        val newManifest = manifest.copy(chunks = manifest.chunks.map { chunkInfo ->
            chunkInfo.copy(file = splitResult.second.find { it.remoteKey == chunkInfo.remoteKey }?.file)
        })
        val testTargetFile = createTempFile(prefix = "test_target", suffix = ".bin").toFile()

        ChunkMerger.mergeWithManifest(newManifest,testTargetFile.absolutePath)
    }

    /**
     * 生成指定大小的随机内容文件
     * @param filePath 文件完整路径
     * @param fileSize 文件大小（单位：字节）
     */
    fun generateRandomFile(filePath: String, fileSize: Long) {
        require(fileSize > 0) { "文件大小必须大于0字节" }
        val random = Random()
        // 使用RandomAccessFile高效写入
        RandomAccessFile(filePath, "rw").use { file ->
            // 设置文件初始大小
            file.setLength(fileSize)

            // 使用缓冲区提高性能（1MB缓冲区）
            val bufferSize = 1024 * 1024
            val buffer = ByteArray(bufferSize)


            var bytesWritten = 0L
            while (bytesWritten < fileSize) {
                // 计算本次写入的字节数
                val remaining = fileSize - bytesWritten
                val chunkSize = if (remaining > bufferSize) bufferSize else remaining.toInt()

                // 生成随机字节
                random.nextBytes(buffer)

                // 写入文件
                file.write(buffer, 0, chunkSize)
                bytesWritten += chunkSize

                // 显示进度
                if (bytesWritten % (100 * bufferSize) == 0L) {
                    val progress = (bytesWritten * 100 / fileSize).toInt()
                    println("generate progress: $progress%")
                }
            }
        }
    }

}
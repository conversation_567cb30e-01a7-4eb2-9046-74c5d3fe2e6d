package com.vega.builder.common.utils

import com.vega.builder.common.logger.logger
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class CoroutineExTest {
    @Test
    fun `test raceOfSuccess`() {
        runBlocking {
            val result = raceOfSuccess({
                delay(200)
                logger().warn("200")
                "200"
            }, {
                delay(300)
                logger().warn("300")

                "300"
            }, {
                delay(400)
                logger().warn("400")
                throw Exception("400")
                "400"
            }, {
                delay(1000)
                "1000"
            })
            logger().info(result)
        }
    }

    @Test
    fun `test currentLimitChannel`() {
        runBlocking {
            currentLimitChannel(listOf(11, 20, 12, 13, 50, 14, 15, 16, 17), 3) { time ->
                logger().warn("$time start")
                delay(200L * time)
                logger().warn("$time end")
            }
        }

    }
}
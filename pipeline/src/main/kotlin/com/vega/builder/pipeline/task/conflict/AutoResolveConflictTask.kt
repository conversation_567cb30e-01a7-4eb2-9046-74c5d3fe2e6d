package com.vega.builder.pipeline.task.conflict

import com.vega.builder.common.airplane.IAirplaneApi
import com.vega.builder.common.airplane.ReportConflictInfoRequest
import com.vega.builder.common.network.request
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.utils.isMockEnv
import com.vega.builder.common.utils.runCommand
import com.vega.builder.common.utils.runCommandWithOutput
import com.vega.builder.common.utils.runCommandWithResult
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.GitParams
import com.vega.builder.pipeline.context.PipelineContextImpl
import com.vega.builder.pipeline.context.WorkspaceParams
import com.vega.builder.pipeline.utils.CoroutineUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import org.koin.core.component.inject
import java.io.File

/**
 *
 *
 * <AUTHOR>
 * @time 2025/1/13
 */

const val TAG = "AutoResolveConflict"

data class ConflictParams(
    val files: List<String>,
    val workspace: String,
    val repoPath: String,
    val sourceBranch: String,
    val targetBranch: String,
)

@TaskDefinition(TAG, stage = Stage.Build, "Automatic conflict resolution")
class AutoResolveConflictTask : PipelineTask() {

    val pipelineContext: PipelineContextImpl by inject()
    val workspaceParams: WorkspaceParams by inject()
    val gitParams: GitParams by inject()

    companion object {
        val supportFileTypes = listOf(
            "gradle.properties",
            "dependency-lock.json",
            "dependency.cmake",
            "Dependence.cmake",
            "Podfile.seer",
            "do_not_modify_strings.xml",
            "do_not_modify_legacy_strings.xml",
            ".kt",
        )
    }

    override suspend fun run() {
        val params = loadConflictParams()
        try {
            // 解析冲突文件
            val conflictFiles = params.files.map {
                return@map params.repoPath + "/" + it
            }
            if (conflictFiles.isEmpty()) {
                throw RuntimeException("No conflicting documents")
            }
            println("conflictFiles: $conflictFiles")
            // Verify that there are conflicting files that cannot be resolved
            conflictFiles.forEach {
                if (!canResolve(it) || it.contains(
                        "languageKey"
                    )) {
                    throw RuntimeException("Conflict files that cannot be resolved: $it")
                }
            }

            val jobs = supportFileTypes.map { fileType ->
                return@map CoroutineScope(Dispatchers.IO).async {
                    return@async runWithFileType(
                        fileType,
                        conflictFiles.filter { it.endsWith(fileType) })
                }
            }

            if (jobs.awaitAll().any { !it }) {
                throw RuntimeException("Conflict files that cannot be resolved")
            }

            // 解决完成，提交代码
            if (!isMockEnv()) {
                val projectDir = File(workspaceParams.targetProjectDir)
                "git add -A".runCommand(projectDir)
                if (listOf("git", "-c", "core.editor=true", "commit", "-m", "'[PaperAirplane] Auto resolve conflict'").runCommandWithResult(
                        projectDir
                    )
                ) {
                    val result =
                        "git push origin ${params.sourceBranch}".runCommandWithResult(projectDir)
                    println("Automatic conflict resolution results：$result")
                    if (!isMockEnv()) {
                        reportResolveResult(result, params.sourceBranch, params.targetBranch)
                    }
                    println("AutoResolveConflictTask finish.")
                    return
                }
            } else {
                println("Automatic conflict resolution results: true")
                println("AutoResolveConflictTask finish.")
            }
        } catch (e: Exception) {
            println("Automatic conflict resolution failed：${e.message}")
            if (!isMockEnv()) {
                reportResolveResult(false, params.sourceBranch, params.targetBranch)
            }
            println("AutoResolveConflictTask finish.")
            throw e
        }
    }

    fun loadConflictParams(): ConflictParams {
        val conflictFiles = "git diff --name-only --diff-filter=U"
            .runCommandWithOutput(File(workspaceParams.targetProjectDir))
            .split('\n')
            .filter { it.isNotEmpty() && it.isNotBlank() }
        val mainBranch = gitParams.mainBranch?: gitParams.mainCommit
        val targetBranch = gitParams.targetBranch?: gitParams.targetCommit
        println("mainBranch: ${gitParams.mainBranch}, mainCommit: ${gitParams.mainCommit}, targetBranch: ${gitParams.targetBranch}, targetCommit: ${gitParams.targetCommit}")
        return ConflictParams(
            conflictFiles,
            pipelineContext.workspace,
            workspaceParams.targetProjectDir,
            mainBranch!!,
            targetBranch!!
        )
    }

    fun canResolve(fileDir: String): Boolean {
        return supportFileTypes.any { fileDir.endsWith(it) }
    }

    suspend fun runWithFileType(fileType: String, conflictFiles: List<String>): Boolean {
        when (fileType) {
            "gradle.properties" -> {
                return gradlePropertiesProcess(conflictFiles)
            }

            "dependency-lock.json" -> {
                return dependencyLockProcess(conflictFiles)
            }

            "dependency.cmake" -> {
                return dependencyCmakeProcess(conflictFiles)
            }

            "Dependence.cmake" -> {
                return dependencyCmakeProcess(conflictFiles)
            }

            "Podfile.seer" -> {
                return podfileSeerProcess(conflictFiles)
            }

            "do_not_modify_strings.xml" -> {
                return i18nProcess(conflictFiles)
            }

            "do_not_modify_legacy_strings.xml" -> {
                return i18nProcess(conflictFiles)
            }
            ".kt" -> {
                return importProcessor(conflictFiles)
            }
        }
        return false
    }

    private suspend fun gradlePropertiesProcess(conflictFiles: List<String>): Boolean {
        // Read file content
        return conflictFiles.map {
            return@map CoroutineScope(CoroutineUtils.CORE).async {
                return@async VegaGradlePropertiesProcessor(it).process()
            }
        }.awaitAll().any { !it }.not()
    }

    private suspend fun dependencyLockProcess(conflictFiles: List<String>): Boolean {
        return conflictFiles.map {
            return@map CoroutineScope(CoroutineUtils.CORE).async {
                return@async DependencyLockProcessor(it).process()
            }
        }.awaitAll().any { !it }.not()
    }

    private suspend fun dependencyCmakeProcess(conflictFiles: List<String>): Boolean {
        return conflictFiles.map {
            return@map CoroutineScope(CoroutineUtils.CORE).async {
                return@async DependencyCmakeProcessor(it).process()
            }
        }.awaitAll().any { !it }.not()
    }

    private suspend fun podfileSeerProcess(conflictFiles: List<String>): Boolean {
        return conflictFiles.map {
            return@map CoroutineScope(CoroutineUtils.CORE).async {
                return@async PodfileSeerProcessor(it).process()
            }
        }.awaitAll().any { !it }.not()
    }

    private suspend fun i18nProcess(conflictFiles: List<String>): Boolean {
        return conflictFiles.map {
            return@map CoroutineScope(CoroutineUtils.CORE).async {
                return@async I18nProcessor(it).process()
            }
        }.awaitAll().any { !it }.not()
    }

    private suspend fun importProcessor(conflictFiles: List<String>): Boolean {
        return conflictFiles.map {
            return@map CoroutineScope(CoroutineUtils.CORE).async {
                return@async KotlinProcessor(it).process()
            }
        }.awaitAll().any {!it }.not()
    }

    private suspend fun reportResolveResult(
        result: Boolean,
        branch: String,
        targetBranch: String
    ) {
        val projectId = getenvSafe("CUSTOM_CI_PROJECT_ID")?.toInt()
        val mrId = getenvSafe("CUSTOM_CI_MR_ID")?.toInt()
        if (projectId == null || mrId == null) {
            println("projectId or mrId is null, skip report, result: $result")
            return
        }
        CoroutineScope(CoroutineUtils.CORE).async {
            request(
                IAirplaneApi::reportConflictInfo, ReportConflictInfoRequest(
                    result,
                    branch,
                    targetBranch,
                    projectId,
                    mrId
                )
            )
        }.await()
        println("reportResolveResult finish.")
    }


}
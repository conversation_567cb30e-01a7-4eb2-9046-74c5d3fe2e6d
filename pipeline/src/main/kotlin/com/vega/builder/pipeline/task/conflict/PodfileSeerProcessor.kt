package com.vega.builder.pipeline.task.conflict

import java.io.File

class PodfileSeerProcessor : FileConflictProcessor {
    constructor(filePath: String) : super(filePath)
    constructor(file: File, fileReader: ConflictFileReader) : super(file, fileReader)

    override suspend fun processConflictBlock(conflictBlock: ConflictBlock): List<String> {
        // First determine whether the number of conflicting rows is the same. Podfile.seer only resolves conflicts with the same number of rows
        if (conflictBlock.conflictLinesHead.size != conflictBlock.conflictLinesInsert.size) {
            throw IllegalStateException("Podfile.seer only supports conflicts with the same number of lines")
        }
        // Determine the version to use by using the version in the conflict block
        // Find the line with version first
        val versionHead = conflictBlock.conflictLinesHead.first { extractSimpleVersion(it).isNotEmpty() }
        val versionInsert =
            conflictBlock.conflictLinesInsert.first { extractSimpleVersion(it).isNotEmpty() }

        val headVersion = extractSimpleVersion(versionHead)
        val insertVersion = extractSimpleVersion(versionInsert)

        // If versions are equal, cannot auto-resolve
        if (headVersion == insertVersion) {
            throw IllegalStateException("Podfile.seer cannot auto-resolve conflicts with equal versions: $headVersion")
        }

        // For simple versions, use version comparison
        return if (isLargeThan(headVersion, insertVersion)) {
            conflictBlock.conflictLinesHead
        } else {
            conflictBlock.conflictLinesInsert
        }
    }

    private fun extractSimpleVersion(input: String): String {
        // Extract simple version from Podfile.seer format
        // Matches patterns like: - DependencyName (1.2.3, from 'repo') or - DependencyName (1.2.3-suffix, from 'repo')
        // For version pattern 1.0.0.144-cc, extract 1.0.0.144

        val versionRegex = """-\s+([\w-]+)\s+\(([0-9]+(?:\.[0-9]+)*(?:[-\w]*)?),\s+from\s+['"].*['"]""".toRegex()
        val versionMatch = versionRegex.find(input)

        if (versionMatch != null) {
            val fullVersion = versionMatch.groupValues[2]
            // Extract only the numeric part (ignore any suffix like -alpha, -beta, -cc, etc.)
            val simpleVersionRegex = """^([0-9]+(?:\.[0-9]+)*)""".toRegex()
            val simpleMatch = simpleVersionRegex.find(fullVersion)
            return simpleMatch?.groupValues?.get(1) ?: ""
        }

        // For git commits, path dependencies or other formats, return empty string (no version to compare)
        return ""
    }
}
package com.vega.builder.pipeline.task.artifacts

import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.PipelineOutput
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.io.File

@Serializable
data class DependencieComponent(
    val componentName: String,
    val componentVersion: String
)

@TaskDefinition(DependenciesJsonTask.TAG, stage = Stage.After, DependenciesJsonTask.TAG,)
class DependenciesJsonTask : PipelineTask() {

    companion object {
        const val TAG = "DependenciesJson"
    }

    override suspend fun run() {
        if (File(PipelineOutput.buildDependency).exists()) {
            File(PipelineOutput.buildSdkDependenciesJson).apply {
                val components = File(PipelineOutput.buildDependency)
                    .readLines()
                    .map { it.trim() }
                    .filter { it.isNotEmpty() }
                    .mapNotNull { line ->
                        val parts = line.split(":")
                        if (parts.size >= 3) {
                            DependencieComponent(
                                componentName = "${parts[0]}:${parts[1]}",
                                componentVersion = parts[2]
                            )
                        } else {
                            null
                        }
                    }

                val jsonOutput = Json.encodeToString(components)

                writeText(jsonOutput)
            }
        }
    }
}

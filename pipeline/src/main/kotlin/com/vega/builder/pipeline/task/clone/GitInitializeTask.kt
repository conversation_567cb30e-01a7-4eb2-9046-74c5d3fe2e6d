package com.vega.builder.pipeline.task.clone

import com.vega.builder.common.logger.logger
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.utils.isMockEnv
import com.vega.builder.common.utils.runCommand
import com.vega.builder.common.utils.runCommandWithBackground
import com.vega.builder.common.utils.runCommandWithOutput
import com.vega.builder.common.utils.runCommandWithOutputNoException
import com.vega.builder.common.utils.runCommandWithResult
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.BuildConfig
import com.vega.builder.pipeline.context.GitParams
import com.vega.builder.pipeline.context.WorkspaceParams
import com.vega.builder.pipeline.utils.GitUtils
import org.koin.core.component.inject
import java.io.File
import kotlin.io.path.Path
import kotlin.io.path.pathString

/**
 *
 *
 * <AUTHOR>
 * @time 2025/1/22
 */
@TaskDefinition("GitInitializeTask", stage = Stage.GitInitialize, "Git Init")
class GitInitializeTask : PipelineTask() {
    val workspaceParams: WorkspaceParams by inject()
    val gitParams: GitParams by inject()

    override suspend fun run() {
        updateSSHKey()
        val clonePath = cloneMainCode()
        mergeTarget(clonePath)
    }

    private fun mergeTarget(clonePath: String) {
        val mergeTarget = gitParams.mergeTarget
        if (mergeTarget == true) {
            //TODO Need to ensure branch integrity
            val targetBranch = gitParams.targetBranch
            val targetCommit = gitParams.targetCommit
            val cloneDir = File(clonePath)
            "git config --global merge.conflictstyle zdiff3".runCommand(cloneDir)
            val mergeMsg = if (targetBranch.isNullOrEmpty()) {
                "git merge $targetCommit --no-edit".runCommandWithOutputNoException(cloneDir)
            } else {
                "git merge origin/$targetBranch --no-edit".runCommandWithOutputNoException(cloneDir)
            }
            println(mergeMsg)
            if (mergeMsg.contains("CONFLICT")) {
                // Conflict occurred, report information
            }
        }
    }

    private fun cloneMainCode(): String {
        // Analyze the Git warehouse name
        val gitUrl = gitParams.mainUrl
        val repoName = GitUtils.extractRepoNameFromGitUrl(gitUrl)
        val branch = gitParams.mainBranch
        val commitId = gitParams.mainCommit
        val targetPath = gitParams.mainTargetPath
        return downloadRepo(gitUrl, repoName, branch, commitId, targetPath)
    }

    private fun downloadRepo(
        gitUrl: String,
        repoName: String,
        branch: String?,
        commitId: String?,
        targetPath: String?
    ): String {
        val workspace = File(workspaceParams.workspace)
        val clonePath = if (targetPath.isNullOrEmpty()) {
            Path(workspaceParams.workspace, repoName).pathString
        } else {
            targetPath
        }
        val cloneFile = File(clonePath)
        if (cloneFile.exists()) {
            val gitConfig = File(clonePath, ".git")
            val isGitRepo = gitConfig.exists()
            if (isGitRepo) {
                // 如果存在仓库，先进行一些清理任务
                logger().info("start git clean")
                "rm -rf $clonePath/.git/index.lock".runCommand(cloneFile)
//                "find . -name *.lock|xargs rm -rf".runCommand(gitConfig)
                "git clean -xdf".runCommand(cloneFile)
                if (!isMockEnv()) {
                    "git gc --auto &".runCommandWithBackground(cloneFile)
                }
                val videoeditorDir = File(workspace, "videoeditor")
                if (videoeditorDir.exists()) {
                    "git clean -xdf".runCommand(videoeditorDir)
                }
            } else {
                // If a folder exists but is not a Git repository, initialize a repository and fetch
                //TODO: This method performs poorly
                logger().info("start git init and full clone")
                "git init".runCommand(cloneFile)
                "git remote add origin $gitUrl".runCommand(cloneFile)
                "git fetch --all -f -p -P".runCommand(cloneFile, false)
            }
            // Pull branches, and don't pull them in mock situations. It's a bit of a waste of time. Pull them manually
            if (!isMockEnv()) {
                "git config remote.origin.fetch +refs/heads/*:refs/remotes/origin/*".runCommand(cloneFile)
                "git config remote.origin.url $gitUrl".runCommand(cloneFile)
                "git remote prune origin".runCommand(cloneFile)
            }

            if (!commitId.isNullOrEmpty()) {
                "git fetch origin".runCommand(cloneFile, false)
                "git rev-parse $commitId^{commit}".runCommand(cloneFile)
                "git log --format=%B -n 1 $commitId".runCommand(cloneFile)
                "git checkout -f $commitId".runCommand(cloneFile, false)
            } else {
                "git fetch origin $branch".runCommand(cloneFile, false)
                val branchCommitId = "git rev-parse origin/$branch^{commit}".runCommandWithOutput(cloneFile)
                "git log --format=%B -n 1 $branchCommitId".runCommand(cloneFile)
                "git checkout -f $branchCommitId".runCommand(cloneFile, false)
                if ("git show-ref --verify --quiet refs/heads/$branch".runCommandWithResult(cloneFile)) {
                    "git branch -D $branch".runCommand(cloneFile, false)
                }
                "git checkout -b $branch $branchCommitId".runCommand(cloneFile, false)
            }
        } else {
            // Unified adoption of partial clones
            "git clone --filter=blob:none --no-checkout $gitUrl".runCommand(workspace, false)
            if (!commitId.isNullOrEmpty()) {
                "git rev-parse $commitId^{commit}".runCommand(cloneFile)
                "git log --format=%B -n 1 $commitId".runCommand(cloneFile)
                "git checkout -f $commitId".runCommand(cloneFile, false)
            } else {
                "git checkout $branch".runCommand(cloneFile, false)
            }
        }
        return clonePath
    }

    private fun updateSSHKey() {
        if (isMockEnv()) {
            return
        }
        val cloudBuildIdRsaKey = getenvSafe("CLOUD_BUILD_ID_RSA_KEY")
        if (!cloudBuildIdRsaKey.isNullOrEmpty()) {
            println("set CLOUD_BUILD_ID_RSA_KEY")
            val process = Runtime.getRuntime().exec("eval \"\$(ssh-agent -s)\"")
            process.waitFor()
            val process2 = Runtime.getRuntime().exec("echo -e \"$cloudBuildIdRsaKey\" | ssh-add -")
            process2.waitFor()
        } else {
            if (System.getProperty("os.name") == "Mac OS X") {
                val process = Runtime.getRuntime()
                    .exec("curl -s set_ssh.sh http://${BuildConfig.hide_tos_url}/obj/cloudbuildstatic/static/********/set_ssh_mac.sh | bash -s --")
                process.waitFor()
            } else {
                val process = Runtime.getRuntime()
                    .exec("curl -s set_ssh.sh http://${BuildConfig.hide_tos_url}/obj/cloudbuildstatic/static/set_ssh.sh | bash -s --")
                process.waitFor()
            }
        }

        val gitAccountName = System.getenv("GIT_ACCOUNT_NAME")
        val gitAccountEmail = System.getenv("GIT_ACCOUNT_EMAIL")
        if (gitAccountName != null && gitAccountName.isNotEmpty() && gitAccountEmail != null && gitAccountEmail.isNotEmpty()) {
            println("set GIT_ACCOUNT_NAME && GIT_ACCOUNT_EMAIL")
            val process = Runtime.getRuntime().exec("git config --global user.name \"$gitAccountName\"")
            process.waitFor()
            val process2 = Runtime.getRuntime().exec("git config --global user.email \"$gitAccountEmail\"")
            process2.waitFor()
        }
    }
}
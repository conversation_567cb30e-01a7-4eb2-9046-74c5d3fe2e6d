package com.vega.builder.pipeline.task.artifacts

import com.google.gson.Gson
import com.vega.builder.common.logger.logger
import com.vega.builder.common.network.api.IJFrogApi
import com.vega.builder.common.network.api.JfrogResp
import com.vega.builder.common.network.request
import com.vega.builder.common.utils.chunk.ChunkSplitter
import com.vega.builder.common.utils.currentLimitChannel
import com.vega.builder.common.utils.fromJson
import com.vega.builder.common.utils.getenvSafe
import com.vega.builder.common.utils.retry
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.PipelineOutput
import com.vega.builder.pipeline.utils.SlardarUtils
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.asRequestBody
import org.koin.core.component.inject
import java.io.File
import kotlin.Boolean
import kotlin.io.path.createTempDirectory
import kotlin.io.path.writeText

const val SYMBOL_UPLOAD_KEY_PREFIX = "CapCut/android/pipeline_result/symbol_"

/**
 * 在TTP环境，会上传符号表和Manifest
 */
@TaskDefinition(
    name = "TTP_SLARDAR_MAPPING_UPLOAD",
    stage = Stage.After,
    allowFailure = false,
    displayName = "Upload TTP Slardar mapping"
)
class TTPSlardarMappingUpload : PipelineTask() {
    val buildParams by inject<BuildParams>()
    override suspend fun run() {
        TTPSymbolUploader(buildParams.isEnable("SLARDAR_NATIVE_PART_UPLOAD", true)).upload()
    }
}

class TTPSymbolUploader(val partUpload: Boolean = false) {
    val uploadBaseKey by lazy { "${SYMBOL_UPLOAD_KEY_PREFIX}${getenvSafe("WORKFLOW_JOB_ID")}" }

    suspend fun upload() {
        val result = uploadSymbol()
        uploadManifest(result)
    }

    private fun querySymbolList(): List<String> {
        val nativePathInfoFile = File(PipelineOutput.buildNativePathInfo)
        return if (nativePathInfoFile.exists()) {
            Gson().fromJson<List<String>>(nativePathInfoFile.readText()).toHashSet().toList()
        } else {
            emptyList()
        }
    }

    suspend fun uploadSymbol(): List<SymbolArtifactInfo> {
        val uploadResult = mutableListOf<SymbolArtifactInfo>()
        val paths = querySymbolList()
        for (path in paths) {
            val soFile = File(path)
            if (!soFile.exists()) {
                continue
            }
            val fileInfo = SlardarUtils.queryNativeFileInfo(soFile.absolutePath)
            if (fileInfo != null) {
                logger().info("File[${soFile.absolutePath}] Build ID: $fileInfo")
                if (!fileInfo.stripped) {
                    // 只有在开启分片上传，并且分片上传的文件大小大于100MB才会启用分片上传
                    uploadResult.add(
                        if (partUpload && soFile.length() > 100 * 1024 * 1024L) {
                            partUpload(fileInfo, soFile)
                        } else {
                            upload(fileInfo, soFile)
                        }
                    )
                } else {
                    logger().warn("File[${soFile.absolutePath}] is stripped,skip upload!")
                }
            } else {
                logger().error("File[${soFile.absolutePath}] not find file info")
            }
        }
        return uploadResult
    }

    private suspend fun upload(
        fileInfo: SlardarUtils.NativeFileInfo,
        soFile: File,
    ): SymbolArtifactInfo {
        val uploadFileKey = "${uploadBaseKey}/file/${fileInfo.buildId}_${soFile.name}"
        return try {
            val uploadFileResult = uploadFileWithRetry(uploadFileKey, soFile)
            SymbolArtifactInfo(
                buildId = fileInfo.buildId,
                name = soFile.name,
                path = uploadFileKey,
                md5 = uploadFileResult.checksums.md5,
                success = true
            )
        } catch (e: Exception) {
            SymbolArtifactInfo(
                buildId = fileInfo.buildId, name = soFile.name, path = uploadFileKey, md5 = "", success = false
            )
        }
    }

    private suspend fun partUpload(fileInfo: SlardarUtils.NativeFileInfo, soFile: File): SymbolArtifactInfo {
        val uploadFileKey = "${uploadBaseKey}/file/${fileInfo.buildId}_${soFile.name}"
        val partDir = createTempDirectory("part_${soFile.nameWithoutExtension}").toFile()
        val (chunkManifestFile, chunkList) = ChunkSplitter.splitFile(
            soFile.absolutePath, partDir.absolutePath, uploadFileKey, 20 * 1024 * 1024 // 先配置20MB
        )
        return try {
            currentLimitChannel(chunkList, 10) { chunkInfo ->
                uploadFileWithRetry(chunkInfo.remoteKey, chunkInfo.file!!)
            }
            val manifestUploadResult = uploadFileWithRetry("${uploadFileKey}/chunk_manifest.json", chunkManifestFile)
            SymbolArtifactInfo(
                buildId = fileInfo.buildId,
                name = soFile.name,
                path = "${uploadFileKey}/chunk_manifest.json",
                md5 = manifestUploadResult.checksums.md5,
                success = true,
                type = "chunk_manifest"
            )
        } catch (_: Exception) {
            SymbolArtifactInfo(
                buildId = fileInfo.buildId, name = soFile.name, path = uploadFileKey, md5 = "", success = false
            )
        }

    }

    suspend fun uploadManifest(symbolArtifactInfo: List<SymbolArtifactInfo>) {
        val resultManifest = kotlin.io.path.createTempFile(prefix = "uploadResult", suffix = ".json")
        val manifestContent = Gson().toJson(symbolArtifactInfo)
        logger().info("manifest content: $manifestContent")
        resultManifest.writeText(manifestContent)
        uploadFileWithRetry("${uploadBaseKey}/manifest.json", resultManifest.toFile())
    }

    suspend fun uploadFileWithRetry(key: String, source: File): JfrogResp {
        return retry(3, 50) {
            val result = request(
                IJFrogApi::upload, key, source.asRequestBody("application/octet-stream".toMediaType())
            )
            if (!result.isSuccessful || result.body() == null) {
                throw Exception("Upload[${key}] error:$uploadBaseKey")
            }
            result.body()!!
        }.apply {
            logger().info("upload result: ${Gson().toJson(this)}")
        }
    }
}


data class SymbolArtifactInfo(
    val buildId: String,
    val name: String,
    val path: String,
    val md5: String,
    val success: Boolean,
    val type: String? = null
)
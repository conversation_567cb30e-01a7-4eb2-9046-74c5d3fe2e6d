package com.vega.builder.pipeline.task.prepare

import com.vega.builder.common.utils.CommonPropertiesEditor
import com.vega.builder.common.workflow.PipelineTask
import com.vega.builder.common.workflow.Stage
import com.vega.builder.common.workflow.TaskDefinition
import com.vega.builder.pipeline.context.BuildParams
import com.vega.builder.pipeline.context.PathDeclare
import com.vega.builder.pipeline.context.PipelineContextImpl
import com.vega.builder.pipeline.context.buildTarget
import com.vega.builder.pipeline.context.isChannelPackage
import com.vega.builder.pipeline.task.prepare.parses.properties.DefaultParse
import com.vega.builder.pipeline.task.prepare.parses.properties.customParse
import org.koin.core.component.inject
import java.io.FileInputStream
import java.util.Properties

/**
 * The first preparation task requires initialization
 */
@TaskDefinition(
    "ChangeBuildProperties",
    stage = Stage.Prepare,
    displayName = "Replace Build Params"
)
class PrepareBuildPropertiesTask : PipelineTask() {
    companion object {
        const val TAG = "ChangeBuildProperties"
    }

    val buildParams: BuildParams by inject()

    val pipelineContext: PipelineContextImpl by inject()

    val pathDeclare: PathDeclare by inject()


    val paramsKeys =
        mapOf(
            "IS_BUILD_64" to DefaultParse(),
            "IS_INTEGRATED_BUILD" to DefaultParse(),
            "APP_VERSION_NAME" to
                    customParse { _, value ->
                        if (buildParams.buildTarget.isOversea) {
                            edit("OVERSEA_APP_VERSION_NAME", value)
                        } else {
                            edit("APP_VERSION_NAME", value)
                        }
                    },
            "APP_VERSION_CODE" to
                    customParse { _, value ->
                        if (value != "RELEASE_VERSION_CODE") {
                            if (buildParams.buildTarget.isOversea) {
                                edit("OVERSEA_APP_VERSION_CODE", value)
                                edit("OVERSEA_UPDATE_VERSION_CODE", value)
                            } else {
                                edit("APP_VERSION_CODE", value)
                                edit("UPDATE_VERSION_CODE", value)
                            }
                        }
                    },
            "IS_OPEN_ANYWHERE" to DefaultParse(pKey = "HAS_ANYWHERE"),
            "IS_COVERAGE" to
                    customParse { _, value ->
                        if (value.toBoolean()) {
                            edit("COVERAGE_ENABLE", "true")
                            edit("COVERAGE_ANNOTATION_VERSION", "0.1.2")
                            edit("COVERAGE_PLUGIN_VERSION", "0.1.7")
                        }
                    },
            "IS_OUT_BUILD" to
                    DefaultParse { _, value ->
                        if (value.toBoolean()) {
                            edit("bytebus.uploadDependency", "true")
                            edit("HAS_ANYWHERE", "false")
                            if (buildParams.isChannelPackage) {
                                edit("IS_CHANNEL_BUILD", "true")
                                edit("IS_GREY_BUILD", "false")
                            } else {
                                edit("IS_CHANNEL_BUILD", "false")
                                edit("IS_GREY_BUILD", "true")
                            }
                        }
                    },
            "IS_UPLOAD_DEPENDENCY" to DefaultParse(),
            "IS_MULTI_ARCH_BUILD" to DefaultParse(),
            "IS_BUILD_BYTEINSIGHT" to DefaultParse(),
            "BYTEINSIGHT_VERSION" to DefaultParse(),
            "EFFECT_VERSION" to DefaultParse(pKey = "FORCE_EFFECT_SDK_VERSION"),
            "FORCE_LYNX_DEPEND_VERSION" to DefaultParse(),
            "FORCE_CLOUD_SDK_VERSION" to
                    customParse { _, value ->
                        if (buildParams.buildTarget.isOversea) {
                            add("FORCE_CLOUD_SDK_VERSION_OVERSEA", value)
                        } else {
                            add("FORCE_CLOUD_SDK_VERSION", value)
                        }
                    },
            "VE_VERSION" to
                    customParse { _, value ->
                        if (buildParams.buildTarget.isOversea) {
                            edit("VE_SDK_VERSION_OVERSEA", value)
                        } else {
                            edit("VE_SDK_VERSION", value)
                        }
                    },
            "IS_CUSTOM_BUILD" to DefaultParse(),
            "LYNX_INSPECT" to DefaultParse(pKey = "LYNX_INSPECT_ENABLE"),
            "IS_BUILD_RHEA_PRO" to DefaultParse(),
            "IS_BUILD_RHEA_3" to DefaultParse(),
            "IS_AUTO_TEST_DOMINO" to DefaultParse(),
            "CUSTOM_BUILD_PARAM" to DefaultParse(),
            "PERF_COMMON_PARAM" to DefaultParse(),
            "hawkeye_mr_diff_current_branch" to DefaultParse(),
            "enable_trace_mrdiff" to DefaultParse(),
            "hawkeye_mr_method_diff_path" to DefaultParse(),
            "hawkeye_mr_diff_path" to DefaultParse(),
            "IS_DATA_AUTOMATION" to DefaultParse(),
            "IS_AUTO_TEST" to DefaultParse(),
            "IS_ASAN_BUILD" to DefaultParse(pKey = "ASAN_BUILD"),
            "HYPER_BUILD" to DefaultParse(),
            "IS_BUILD_WITH_MATERIAL_HOOK" to DefaultParse(),
            "ENABLE_DEVKIT" to DefaultParse(),
            "BUILD_TARGET" to DefaultParse(pKey = "build.target"),
            "DEBUG_FIX" to DefaultParse(),
            "IS_BUILD_SIZE" to DefaultParse(),
            "IS_BYTE_TEST_BUILD" to DefaultParse(),
            "IS_GRADLE_CACHE_PUBLISH" to DefaultParse(pKey = "GRADLE_CACHE_PUBLISH"),
            "IS_BUILD_LIB_DEVELOPE" to DefaultParse(),
            "isUseTTPMaven" to DefaultParse(),
        )

    override suspend fun run() {
        Properties().apply {
            load(FileInputStream(pathDeclare.gradlePropertiesFile))
            this["APP_VERSION_CODE_DEFAULT"] = this.getProperty("APP_VERSION_CODE")
            this["OVERSEA_APP_VERSION_CODE_DEFAULT"] = this.getProperty("OVERSEA_APP_VERSION_CODE")
        }
        CommonPropertiesEditor(pathDeclare.gradlePropertiesFile).use { editor ->
            buildParams.filterValues { it.isNotBlank() }.forEach { (key, value) ->
                paramsKeys[key]?.apply {
                    editor.parse(key, value)
                }
            }
            editor.edit(
                "org.gradle.jvmargs",
                "-Xmx46192M -Dkotlin.daemon.jvm.options\\=\"-Xmx20480M\""
            )
        }
    }
}
package com.vega.builder.pipeline.context

import com.vega.builder.common.utils.getenvSafe


fun createGitParams(buildParams: BuildParams): GitParams {
    val targetBranch = if (getenvSafe("TARGET_BRANCH", "").isNotEmpty()) {
        getenvSafe("TARGET_BRANCH", "")
    } else {
        buildParams["TARGET_BRANCH"]
    }
    val targetCommit = if (getenvSafe("TARGET_COMMIT", "").isNotEmpty()) {
        getenvSafe("TARGET_COMMIT", "")
    } else {
        buildParams["TARGET_COMMIT"]
    }

    val mergeTarget = if (getenvSafe("MERGE_TARGET", "").isNotEmpty()) {
        getenvSafe("MERGE_TARGET", "false").toBoolean()
    } else {
        buildParams["MERGE_TARGET"].toBoolean()
    }
    val mainBranch = if (getenvSafe("MAIN_GIT_BRANCH", "").isNotEmpty()) {
        getenvSafe("MAIN_GIT_BRANCH", "")
    } else {
        buildParams["MAIN_GIT_BRANCH"]
    }

    val mainCommit = if (getenvSafe("MAIN_GIT_COMMIT", "").isNotEmpty()) {
        getenvSafe("MAIN_GIT_COMMIT", "")
    } else {
        buildParams["MAIN_GIT_COMMIT"]
    }

    return GitParams(
        mainUrl = getenvSafe("MAIN_GIT_URL", ""),
        mainBranch = mainBranch,
        mainCommit = mainCommit,
        mainTargetPath = getenvSafe("MAIN_CODE_TARGERT_PATH", ""),
        clonePath = getenvSafe("CLONE_PATH", ""),
        cleanAll = getenvSafe("CLEAN_ALL", "true").toBoolean(),
        gitCleanExtraParams = getenvSafe("CLEAN_EXTRA_PARAMS", ""),
        submoduleIgnore = getenvSafe("SUBMODULE_IGNORE", "true").toBoolean(),
        targetBranch = targetBranch,
        targetCommit = targetCommit,
        mergeTarget = mergeTarget,
    )
}

data class GitParams(
    val mainUrl: String,
    val mainBranch: String?,
    val mainCommit: String?,
    val mainTargetPath: String?,
    val clonePath: String?,
    val cleanAll: Boolean?,
    val gitCleanExtraParams: String?,
    val submoduleIgnore: Boolean?,
    val targetBranch: String?,
    val targetCommit: String?,
    val mergeTarget: Boolean?,
)